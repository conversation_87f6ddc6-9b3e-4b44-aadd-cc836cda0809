from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Dict, Any
from datetime import datetime
import enum
import json # Added for parsing JSON strings

# Re-define enum for Pydantic validation if needed, or reuse db.models.TaskStatus carefully
class TaskStatusEnum(str, enum.Enum):
    PENDING = "PENDING"
    GENERATING = "GENERATING"
    COMPLETED = "COMPLETED"
    EVALUATING = "EVALUATING"
    EVALUATION_DONE = "EVALUATION_DONE"
    FAILED = "FAILED"

# --- Generation Schemas ---
class GenerationBase(BaseModel):
    model_id_used: str
    output_text: Optional[str] = None
    reasoning_text: Optional[str] = None  # Add field for storing model reasoning
    error_message: Optional[str] = None

    # Usage statistics fields
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    total_tokens: Optional[int] = None
    cost_credits: Optional[float] = None  # Cost in dollars from OpenRouter API
    cached_tokens: Optional[int] = None  # For cached token tracking
    reasoning_tokens: Optional[int] = None  # For reasoning token tracking

class GenerationCreate(GenerationBase):
    task_id: int
    blind_id: str # Was Optional[str]

class Generation(GenerationBase):
    id: int
    task_id: int
    blind_id: str # Was Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True # Replaces orm_mode=True in Pydantic v2

# --- Ranking Schemas ---
class RankingBase(BaseModel):
    evaluator_model_id: str
    ranked_list_json: Optional[List[int]] = None
    reasoning_text: Optional[str] = None
    error_message: Optional[str] = None

    # Usage statistics fields
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    total_tokens: Optional[int] = None
    cost_credits: Optional[float] = None  # Cost in dollars from OpenRouter API
    cached_tokens: Optional[int] = None  # For cached token tracking
    reasoning_tokens: Optional[int] = None  # For reasoning token tracking

class RankingCreate(RankingBase):
    pass # evaluation_id will be added by CRUD

class Ranking(RankingBase):
    id: int
    evaluation_id: int
    created_at: datetime

    class Config:
        from_attributes = True

# --- Evaluation Schemas ---
class EvaluationBase(BaseModel):
    status: TaskStatusEnum = TaskStatusEnum.PENDING
    evaluation_used_blind_ids: bool
    evaluation_prompt: Optional[str] = None  # Store the evaluation prompt used

class EvaluationCreate(BaseModel):
    evaluator_models: List[str] = Field(..., min_length=1)
    evaluation_used_blind_ids: bool # Added required field

class Evaluation(EvaluationBase):
    id: int
    task_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    rankings: List[Ranking] = [] # Include rankings when retrieving an evaluation
    # evaluation_used_blind_ids and evaluation_prompt are inherited from EvaluationBase

    class Config:
        from_attributes = True


# --- Task Schemas ---
class TaskBase(BaseModel):
    prompt: str
    system_prompt: Optional[str] = None  # New field for system prompt

class TaskCreate(TaskBase):
    models_to_generate: Optional[List[str]] = None # Optional: Specify models, otherwise use default
    # use_blind_ids: Optional[bool] = True # REMOVED

class Task(TaskBase):
    id: int
    status: TaskStatusEnum
    requested_models: Optional[List[str]] = None # Models originally requested
    created_at: datetime
    updated_at: Optional[datetime] = None
    generations: List[Generation] = []
    evaluations: List[Evaluation] = [] # Include evaluations related to the task

    class Config:
        from_attributes = True # Allows creating Pydantic models from ORM objects

# --- API Specific Schemas ---
class TaskCreateRequest(BaseModel):
    prompt: str
    system_prompt: Optional[str] = None  # New field for system prompt
    # Add models_to_generate if you want client to specify them
    models_to_generate: Optional[List[str]] = None
    # use_blind_ids: Optional[bool] = True # REMOVED

class TaskCreateResponse(BaseModel):
    task_id: int
    status: TaskStatusEnum
    requested_models: List[str] # Add the list of models being generated
    message: str

class TaskStatusResponse(BaseModel):
    task_id: int
    status: TaskStatusEnum
    generations: List[Generation] # Return generated outputs

class EvaluateRequest(BaseModel):
    evaluator_models: List[str] = Field(..., min_length=1)
    evaluation_used_blind_ids: bool # Added required field
    custom_evaluation_prompt: Optional[str] = None  # New: Custom evaluation prompt

class EvaluateResponse(BaseModel):
    evaluation_id: int
    status: TaskStatusEnum
    message: str

class EvaluationStatusResponse(BaseModel): # New Schema
    evaluation_id: int
    task_id: int
    status: TaskStatusEnum

class EvaluationReportResponse(BaseModel):
    evaluation_id: int
    task_id: int
    status: TaskStatusEnum
    rankings: List[Ranking] # The core report data

class ModelListResponse(BaseModel):
    models: List[str]

# --- History Schemas ---
class TaskHistoryItem(BaseModel):
    id: int
    prompt_snippet: str # A short part of the prompt
    status: TaskStatusEnum
    created_at: datetime

    class Config:
        from_attributes = True

class TaskHistoryResponse(BaseModel):
    history: List[TaskHistoryItem] 

# --- Aggregation Schemas ---
class AggregationAlgorithmEnum(str, enum.Enum):
    AVERAGE_RANK = "average_rank"
    BORDA_COUNT = "borda_count"
    WEIGHTED_AVERAGE_RANK = "weighted_average_rank"
    COPELAND_METHOD = "copeland_method"
    # Add more algorithms here in the future

class AggregatedRankingItem(BaseModel):
    model_id_used: str # The model whose output is being ranked
    aggregated_score: float # The final score after aggregation
    original_ranks: Dict[str, int] # Dict of evaluator_model_id to rank given by that evaluator
    rank: int # The final rank based on aggregated_score

class EvaluatorConsistency(BaseModel):
    evaluator_model_id: str
    consistency_score: Optional[float] = None # e.g., Kendall Tau with the aggregated list
    error_message: Optional[str] = None # If consistency calculation failed

class EvaluationAggregationRequest(BaseModel):
    algorithm: AggregationAlgorithmEnum = AggregationAlgorithmEnum.AVERAGE_RANK
    evaluator_weights: Optional[Dict[str, float]] = None # New: model_id -> weight

class EvaluationAggregationResponse(BaseModel):
    evaluation_id: int
    task_id: int
    algorithm_used: AggregationAlgorithmEnum
    aggregated_rankings: List[AggregatedRankingItem]
    evaluator_consistencies: List[EvaluatorConsistency]
    overall_consistency: Optional[float] = None # e.g., Average Kendall Tau or Fleiss' Kappa
    error_message: Optional[str] = None # If aggregation itself failed

# --- Usage Statistics Schemas ---
class TaskUsageStatsResponse(BaseModel):
    task_id: int
    total_prompt_tokens: int
    total_completion_tokens: int
    total_tokens: int
    total_cost_credits: float
    total_cached_tokens: int
    total_reasoning_tokens: int
    generation_count: int
    ranking_count: int
    models_used: List[str]
    evaluators_used: List[str]
    average_cost_per_generation: float