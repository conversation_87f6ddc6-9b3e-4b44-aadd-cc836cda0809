import React, { useMemo } from 'react';
import { Generation, Ranking } from '../api/apiClient';
import { BarChart3, DollarSign, Zap, TrendingUp, Users, Brain } from 'lucide-react';

interface TaskUsageStatsDisplayProps {
  generations: Generation[];
  rankings?: Ranking[];
  title?: string;
}

interface AggregatedStats {
  totalPromptTokens: number;
  totalCompletionTokens: number;
  totalTokens: number;
  totalCostCredits: number;
  totalCachedTokens: number;
  totalReasoningTokens: number;
  generationCount: number;
  rankingCount: number;
  modelsUsed: Set<string>;
  evaluatorsUsed: Set<string>;
}

const TaskUsageStatsDisplay: React.FC<TaskUsageStatsDisplayProps> = ({ 
  generations, 
  rankings = [],
  title = "Task Usage Summary"
}) => {
  const aggregatedStats = useMemo((): AggregatedStats => {
    const stats: AggregatedStats = {
      totalPromptTokens: 0,
      totalCompletionTokens: 0,
      totalTokens: 0,
      totalCostCredits: 0,
      totalCachedTokens: 0,
      totalReasoningTokens: 0,
      generationCount: 0,
      rankingCount: 0,
      modelsUsed: new Set(),
      evaluatorsUsed: new Set()
    };

    // Aggregate generation stats
    generations.forEach(gen => {
      if (gen.prompt_tokens) stats.totalPromptTokens += gen.prompt_tokens;
      if (gen.completion_tokens) stats.totalCompletionTokens += gen.completion_tokens;
      if (gen.total_tokens) stats.totalTokens += gen.total_tokens;
      if (gen.cost_credits) stats.totalCostCredits += gen.cost_credits;
      if (gen.cached_tokens) stats.totalCachedTokens += gen.cached_tokens;
      if (gen.reasoning_tokens) stats.totalReasoningTokens += gen.reasoning_tokens;
      
      if (gen.output_text && !gen.error_message) {
        stats.generationCount++;
        stats.modelsUsed.add(gen.model_id_used);
      }
    });

    // Aggregate ranking stats
    rankings.forEach(ranking => {
      if (ranking.prompt_tokens) stats.totalPromptTokens += ranking.prompt_tokens;
      if (ranking.completion_tokens) stats.totalCompletionTokens += ranking.completion_tokens;
      if (ranking.total_tokens) stats.totalTokens += ranking.total_tokens;
      if (ranking.cost_credits) stats.totalCostCredits += ranking.cost_credits;
      if (ranking.cached_tokens) stats.totalCachedTokens += ranking.cached_tokens;
      if (ranking.reasoning_tokens) stats.totalReasoningTokens += ranking.reasoning_tokens;
      
      if (ranking.ranked_list_json && !ranking.error_message) {
        stats.rankingCount++;
        stats.evaluatorsUsed.add(ranking.evaluator_model_id);
      }
    });

    return stats;
  }, [generations, rankings]);

  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  const formatCost = (costInDollars: number): string => {
    return `$${costInDollars.toFixed(6)}`;
  };

  const hasUsageData = aggregatedStats.totalTokens > 0 || aggregatedStats.totalCostCredits > 0;

  if (!hasUsageData) {
    return (
      <div className="bg-light-component-subtle dark:bg-dark-component-subtle rounded-xl p-4 border border-light-border dark:border-dark-border">
        <div className="flex items-center gap-2 mb-2">
          <BarChart3 size={18} className="text-light-secondary dark:text-dark-secondary" />
          <h3 className="text-sm font-semibold text-light-primary dark:text-dark-primary">{title}</h3>
        </div>
        <p className="text-sm text-light-secondary dark:text-dark-secondary">
          No usage data available yet. Usage statistics will appear after API calls complete.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-light-component dark:bg-dark-component rounded-xl p-6 border border-light-border dark:border-dark-border">
      <div className="flex items-center gap-2 mb-6">
        <BarChart3 size={20} className="text-light-secondary dark:text-dark-secondary" />
        <h3 className="text-lg font-semibold text-light-primary dark:text-dark-primary">{title}</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Usage */}
        <div className="space-y-4">
          <h4 className="text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">
            Total Usage
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap size={16} className="text-purple-500" />
                <span className="text-sm text-light-primary dark:text-dark-primary">Total Tokens</span>
              </div>
              <span className="text-sm font-bold text-light-primary dark:text-dark-primary">
                {formatNumber(aggregatedStats.totalTokens)}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DollarSign size={16} className="text-green-500" />
                <span className="text-sm text-light-primary dark:text-dark-primary">Total Cost</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-bold text-light-primary dark:text-dark-primary">
                  {formatCost(aggregatedStats.totalCostCredits)}
                </div>
                <div className="text-xs text-light-secondary dark:text-dark-secondary">
                  {formatNumber(Math.round(aggregatedStats.totalCostCredits * 1000000))} credits
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Token Breakdown */}
        <div className="space-y-4">
          <h4 className="text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">
            Token Breakdown
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-light-primary dark:text-dark-primary">Prompt</span>
              <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                {formatNumber(aggregatedStats.totalPromptTokens)}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-light-primary dark:text-dark-primary">Completion</span>
              <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                {formatNumber(aggregatedStats.totalCompletionTokens)}
              </span>
            </div>
            
            {aggregatedStats.totalCachedTokens > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-light-primary dark:text-dark-primary">Cached</span>
                <span className="text-sm font-medium text-orange-500">
                  {formatNumber(aggregatedStats.totalCachedTokens)}
                </span>
              </div>
            )}
            
            {aggregatedStats.totalReasoningTokens > 0 && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <Brain size={12} className="text-indigo-500" />
                  <span className="text-sm text-light-primary dark:text-dark-primary">Reasoning</span>
                </div>
                <span className="text-sm font-medium text-indigo-500">
                  {formatNumber(aggregatedStats.totalReasoningTokens)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Activity Summary */}
        <div className="space-y-4">
          <h4 className="text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">
            Activity Summary
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingUp size={16} className="text-blue-500" />
                <span className="text-sm text-light-primary dark:text-dark-primary">Generations</span>
              </div>
              <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                {aggregatedStats.generationCount}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users size={16} className="text-indigo-500" />
                <span className="text-sm text-light-primary dark:text-dark-primary">Evaluations</span>
              </div>
              <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                {aggregatedStats.rankingCount}
              </span>
            </div>
          </div>
        </div>

        {/* Models Used */}
        <div className="space-y-4">
          <h4 className="text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">
            Models Used
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-light-primary dark:text-dark-primary">Generators</span>
              <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                {aggregatedStats.modelsUsed.size}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-light-primary dark:text-dark-primary">Evaluators</span>
              <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                {aggregatedStats.evaluatorsUsed.size}
              </span>
            </div>
            
            {/* Average cost per generation */}
            {aggregatedStats.generationCount > 0 && (
              <div className="flex items-center justify-between border-t border-light-border dark:border-dark-border pt-2">
                <span className="text-sm text-light-secondary dark:text-dark-secondary">Avg per gen</span>
                <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                  {formatCost(aggregatedStats.totalCostCredits / aggregatedStats.generationCount)}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskUsageStatsDisplay;
