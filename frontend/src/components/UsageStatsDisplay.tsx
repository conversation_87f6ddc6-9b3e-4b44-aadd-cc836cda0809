import React from 'react';
import { Generation, Ranking } from '../api/apiClient';
import { BarChart3, DollarSign, Zap, Clock, Brain, Database } from 'lucide-react';

interface UsageStatsDisplayProps {
  generation?: Generation;
  ranking?: Ranking;
  title?: string;
  compact?: boolean;
}

const UsageStatsDisplay: React.FC<UsageStatsDisplayProps> = ({ 
  generation, 
  ranking, 
  title = "Usage Statistics",
  compact = false 
}) => {
  const data = generation || ranking;
  
  if (!data) {
    return null;
  }

  // Check if we have any usage data
  const hasUsageData = data.prompt_tokens !== null || 
                      data.completion_tokens !== null || 
                      data.total_tokens !== null || 
                      data.cost_credits !== null ||
                      data.cached_tokens !== null ||
                      data.reasoning_tokens !== null;

  if (!hasUsageData) {
    return null;
  }

  const formatNumber = (num: number | null | undefined): string => {
    if (num === null || num === undefined) return 'N/A';
    return num.toLocaleString();
  };

  const formatCost = (costInDollars: number | null | undefined): string => {
    if (costInDollars === null || costInDollars === undefined) return 'N/A';
    // Cost is already in dollars from OpenRouter API
    const creditsEquivalent = Math.round(costInDollars * 1000000);
    return `$${costInDollars.toFixed(6)} (${creditsEquivalent.toLocaleString()} credits)`;
  };

  if (compact) {
    return (
      <div className="bg-light-component-subtle dark:bg-dark-component-subtle rounded-lg p-3 border border-light-border dark:border-dark-border">
        <div className="flex items-center gap-2 mb-2">
          <BarChart3 size={14} className="text-light-secondary dark:text-dark-secondary" />
          <span className="text-xs font-medium text-light-secondary dark:text-dark-secondary">{title}</span>
        </div>
        <div className="grid grid-cols-2 gap-2 text-xs">
          {data.total_tokens !== null && (
            <div className="flex items-center gap-1">
              <Zap size={12} className="text-blue-500" />
              <span className="text-light-primary dark:text-dark-primary">{formatNumber(data.total_tokens)} tokens</span>
            </div>
          )}
          {data.cost_credits !== null && (
            <div className="flex items-center gap-1">
              <DollarSign size={12} className="text-green-500" />
              <span className="text-light-primary dark:text-dark-primary">${data.cost_credits.toFixed(6)}</span>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-light-component dark:bg-dark-component rounded-xl p-4 border border-light-border dark:border-dark-border">
      <div className="flex items-center gap-2 mb-4">
        <BarChart3 size={18} className="text-light-secondary dark:text-dark-secondary" />
        <h3 className="text-sm font-semibold text-light-primary dark:text-dark-primary">{title}</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Token Usage */}
        <div className="space-y-3">
          <h4 className="text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">Token Usage</h4>
          
          {data.prompt_tokens !== null && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock size={14} className="text-blue-500" />
                <span className="text-sm text-light-primary dark:text-dark-primary">Prompt</span>
              </div>
              <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                {formatNumber(data.prompt_tokens)}
              </span>
            </div>
          )}
          
          {data.completion_tokens !== null && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap size={14} className="text-green-500" />
                <span className="text-sm text-light-primary dark:text-dark-primary">Completion</span>
              </div>
              <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                {formatNumber(data.completion_tokens)}
              </span>
            </div>
          )}
          
          {data.total_tokens !== null && (
            <div className="flex items-center justify-between border-t border-light-border dark:border-dark-border pt-2">
              <div className="flex items-center gap-2">
                <BarChart3 size={14} className="text-purple-500" />
                <span className="text-sm font-medium text-light-primary dark:text-dark-primary">Total</span>
              </div>
              <span className="text-sm font-bold text-light-primary dark:text-dark-primary">
                {formatNumber(data.total_tokens)}
              </span>
            </div>
          )}
        </div>

        {/* Advanced Token Details */}
        {(data.cached_tokens !== null || data.reasoning_tokens !== null) && (
          <div className="space-y-3">
            <h4 className="text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">Advanced</h4>
            
            {data.cached_tokens !== null && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Database size={14} className="text-orange-500" />
                  <span className="text-sm text-light-primary dark:text-dark-primary">Cached</span>
                </div>
                <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                  {formatNumber(data.cached_tokens)}
                </span>
              </div>
            )}
            
            {data.reasoning_tokens !== null && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Brain size={14} className="text-indigo-500" />
                  <span className="text-sm text-light-primary dark:text-dark-primary">Reasoning</span>
                </div>
                <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                  {formatNumber(data.reasoning_tokens)}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Cost */}
        {data.cost_credits !== null && (
          <div className="space-y-3">
            <h4 className="text-xs font-medium text-light-secondary dark:text-dark-secondary uppercase tracking-wider">Cost</h4>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DollarSign size={14} className="text-green-500" />
                <span className="text-sm text-light-primary dark:text-dark-primary">Total Cost</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium text-light-primary dark:text-dark-primary">
                  ${data.cost_credits.toFixed(6)}
                </div>
                <div className="text-xs text-light-secondary dark:text-dark-secondary">
                  {formatNumber(Math.round(data.cost_credits * 1000000))} credits
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UsageStatsDisplay;
